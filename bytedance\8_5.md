## 现状分析
### 核心优势分析:

**技术能力**:

* 学历与方向: 南方科技大学，计算机科学硕士，研究方向为3D视觉 (NeRF/SLAM)。

* 技术栈: 掌握 Python/C++ 及 PyTorch 深度学习框架。

* 项目经验: 有主导大型科研项目的经验，具备将算法理论付诸实践的能力。

**项目管理与业务经验**:

* 实习背景: 拥有在字节跳动 TikTok 担任项目管理（PMO）的实习经验。

* 业务领域: 深入参与过推荐系统的项目流程，包括模型审查、Badcase分析及A/B实验。

* 量化成果: 通过优化项目流程，驱动核心业务指标增长45%。

### 潜在弱点分析
   
**算法工程能力**:

* 过往结果: 上一年度秋招中，在纯算法岗位的求职结果未达预期。

* 潜在差距: 面对顶尖算法岗的高强度面试，工程算法的熟练度可能存在不足。

**研究方向与岗位匹配度**:

* 领域偏差: 硕士研究方向（3D视觉）与当前业界最大热点（如LLM）不完全一致。

* 岗位数量: 直接对口的工业界岗位相对较少，需强调技能的迁移性。

**复合背景的定位风险**:

* 定位疑虑: “科研+PMO”的双重背景，可能让部分面试官对长期职业规划的专注度产生疑问。

### 目标方向画像（个人倾向）:

**核心职责**:

* 驱动而非执行: 工作的核心是驱动一个复杂的AI/算法项目从概念到落地，而不是仅仅作为执行环节中的一个螺丝钉。

* 连接技术与业务: 将作为技术团队与业务/产品团队之间的关键桥梁，负责翻译业务需求、拆解技术任务、评估技术风险、跟进项目成果。

* 技术决策参与者: 需要利用技术背景，深入理解方案细节，并参与到技术选型和架构讨论中，确保方案的合理性与前瞻性。

**能力要求**:

* 技术理解力 > 编程实现能力: 岗位更看重对AI算法原理的深刻理解、技术判断力和系统设计能力，而对“手撕代码”的要求相对次要。

* 项目管理能力是核心: 要求具备出色的项目管理和流程优化能力，能将字节PMO的经验直接复用。

* 成果导向的业务思维: 必须具备数据驱动的业务Sense，能够将项目成果与核心业务指标的增长强关联（完美匹配+45%的经历）。

**典型岗位名称**:

首选: 技术产品经理 (Technical Product Manager, TPM)

次选: AI/算法方向的项目经理 (AI-focused Project Manager)

备选: 部分偏向业务应用或系统架构的算法工程师（这类岗位更看重业务理解和系统设计，而非极致的算法竞赛能力）

## 方向和机会：

### 求职策略
根据前面的优劣势分析，求职策略可以聚焦于以下两条核心路径：

**路径一：深耕技术路线（算法工程师）**
核心逻辑: 最大化利用科研深度。

领域要求: 岗位需要与3D视觉/三维重建研究背景强相关。这是在纯技术竞争中的主要优势。

能力侧重: 优先考察极致的技术理论深度和过硬的编程实现能力。PMO经验在此路径下是加分项，体现了工程素养。

**路径二：技术驱动路线（技术产品/项目经理）**
核心逻辑: 最大化利用复合背景和项目影响力。

领域要求: 对具体技术领域的直接相关性要求较弱。核心价值是“能推动复杂AI项目落地”，这个能力可以迁移到大模型、AIGC等任何热门领域。

能力侧重: 优先考察项目管理能力、跨团队沟通、业务理解和宏观的技术判断力。科研背景在此路径下是核心优势，证明具备深度技术理解力。

### 岗位

**抖音产品工程师**

![alt text](image-2.png)

![alt text](image-3.png)

![alt text](image-4.png)

![alt text](image-5.png)（倾向）

**三维重建算法**:

![alt text](image.png)

![alt text](image-1.png)

**项目经理**:

![alt text](image-6.png)

**产品经理**

待完善

## 问题讨论：

**关于怎么选岗位**
* 大多岗位区分很细，所需基础能力均类似，我要如何明确区分出岗位差别并进行选择？（是否仅需考虑个人感觉）

* 我只有两次投简历的机会，该怎么用最保险？

**关于简历和项目**
* 我想申大模型方向的岗位，但我的相关项目还在做，没做完。这个没做完的项目可以写在简历上吗？该怎么写比较好？

* 我是现在就交简历，抢个先手？还是等一两个月，把简历改得更好了再交？哪个更划算？

* 我的情况是“技术”和“项目管理”都会。投不同的岗位时，简历该怎么调整？

